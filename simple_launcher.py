#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单服务启动器
- 显示所有命令在文本框中
- 支持直接编辑命令
- 保存和运行功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import subprocess
import os
import threading
import time
from tkinter import font

class SimpleLauncher:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Windows服务启动器")
        self.root.geometry("900x700")
        self.root.resizable(True, True)

        # 设置窗口图标和样式
        self.setup_window_style()

        # 设置主题样式
        self.setup_theme()



        # 配置文件路径
        self.config_file = "commands.txt"

        # 默认命令列表 - 从文件加载
        self.default_commands = self.load_default_commands_from_file()

        # 状态变量
        self.is_running = False

        # 创建界面
        self.create_widgets()

        # 加载命令
        self.load_commands()

        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def setup_window_style(self):
        """设置窗口样式"""
        # 设置窗口背景色 - 更柔和的背景
        self.root.configure(bg='#f8f9fa')

        # 居中显示窗口
        self.center_window()

        # 设置最小窗口大小
        self.root.minsize(800, 600)

    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')

    def setup_theme(self):
        """设置主题样式"""
        style = ttk.Style()

        # 设置主题 - 使用更兼容的主题
        try:
            # 尝试使用不同的主题，按优先级排序
            available_themes = style.theme_names()
            preferred_themes = ['clam', 'alt', 'default', 'classic']

            theme_to_use = 'default'  # 默认主题
            for theme in preferred_themes:
                if theme in available_themes:
                    theme_to_use = theme
                    break

            style.theme_use(theme_to_use)
            print(f"使用主题: {theme_to_use}")
        except Exception as e:
            print(f"设置主题失败: {e}")
            # 使用系统默认主题
            pass

        # 获取可用字体
        def get_safe_font(preferred_fonts, size, weight='normal'):
            """获取安全的字体配置"""
            for font_name in preferred_fonts:
                try:
                    test_font = font.Font(family=font_name, size=size, weight=weight)
                    return (font_name, size, weight)
                except:
                    continue
            # 如果所有字体都失败，返回系统默认字体
            return ('TkDefaultFont', size, weight)

        # 自定义样式 - 改进颜色协调性
        try:
            title_font = get_safe_font(['Microsoft YaHei UI', 'SimHei', 'Arial', 'Helvetica'], 18, 'bold')
            style.configure('Title.TLabel',
                           font=title_font,
                           foreground='#2c3e50',  # 深蓝灰色
                           background='#f8f9fa')  # 更柔和的背景色
        except Exception as e:
            print(f"配置标题样式失败: {e}")
            style.configure('Title.TLabel', foreground='#2c3e50')

        try:
            subtitle_font = get_safe_font(['Microsoft YaHei UI', 'SimHei', 'Arial', 'Helvetica'], 10)
            style.configure('Subtitle.TLabel',
                           font=subtitle_font,
                           foreground='#6c757d',  # 中性灰色
                           background='#f8f9fa')
        except Exception as e:
            print(f"配置副标题样式失败: {e}")
            style.configure('Subtitle.TLabel', foreground='#6c757d')

        # 配置标准 TLabelFrame 样式
        style.configure('TLabelFrame',
                       background='#f8f9fa',
                       borderwidth=1,
                       relief='solid')

        # 配置 TLabelFrame 标签样式
        label_font = get_safe_font(['Microsoft YaHei UI', 'SimHei', 'Arial', 'Helvetica'], 11, 'bold')
        style.configure('TLabelFrame.Label',
                       font=label_font,
                       foreground='#495057',
                       background='#f8f9fa')

        print("TLabelFrame 样式配置成功")

        # 按钮样式 - 重新设计，解决大小和颜色问题
        try:
            # 通用按钮字体
            button_font = get_safe_font(['Microsoft YaHei UI', 'SimHei', 'Arial', 'Helvetica'], 10, 'normal')

            # 主要操作按钮样式 - 蓝色系
            style.configure('Action.TButton',
                           font=button_font,
                           foreground='white',
                           background='#4a90e2',
                           borderwidth=1,
                           relief='raised',
                           focuscolor='none',
                           padding=(8, 6))

            style.map('Action.TButton',
                     background=[('active', '#357abd'),
                               ('pressed', '#2968a3'),
                               ('disabled', '#bdc3c7')])

            print("Action 按钮样式配置成功")
        except Exception as e:
            print(f"配置 Action 按钮样式失败: {e}")

        try:
            # 成功/运行按钮样式 - 绿色系，更大字体
            success_font = get_safe_font(['Microsoft YaHei UI', 'SimHei', 'Arial', 'Helvetica'], 12, 'bold')
            style.configure('Success.TButton',
                           font=success_font,
                           foreground='white',
                           background='#2ecc71',
                           borderwidth=1,
                           relief='raised',
                           focuscolor='none',
                           padding=(12, 8))

            style.map('Success.TButton',
                     background=[('active', '#27ae60'),
                               ('pressed', '#229954'),
                               ('disabled', '#95a5a6')])

            print("Success 按钮样式配置成功")
        except Exception as e:
            print(f"配置 Success 按钮样式失败: {e}")

        try:
            # 警告/删除按钮样式 - 橙红色系
            style.configure('Warning.TButton',
                           font=button_font,
                           foreground='white',
                           background='#e67e22',
                           borderwidth=1,
                           relief='raised',
                           focuscolor='none',
                           padding=(8, 6))

            style.map('Warning.TButton',
                     background=[('active', '#d35400'),
                               ('pressed', '#ba4a00'),
                               ('disabled', '#bdc3c7')])

            print("Warning 按钮样式配置成功")
        except Exception as e:
            print(f"配置 Warning 按钮样式失败: {e}")

        # 状态栏样式 - 更新颜色协调性
        try:
            status_font = get_safe_font(['Microsoft YaHei UI', 'SimHei', 'Arial', 'Helvetica'], 9)
            style.configure('Status.TLabel',
                           font=status_font,
                           foreground='#495057',  # 协调的文字颜色
                           background='#e9ecef',  # 柔和的背景色
                           relief='flat',         # 扁平化设计
                           borderwidth=1,
                           padding=(8, 4))       # 增加内边距
        except Exception as e:
            print(f"配置状态栏样式失败: {e}")
            style.configure('Status.TLabel',
                           foreground='#495057',
                           background='#e9ecef',
                           relief='flat',
                           borderwidth=1,
                           padding=(8, 4))



    def create_labelframe(self, parent, text, padding="10"):
        """创建 LabelFrame"""
        return ttk.LabelFrame(parent, text=text, padding=padding)

    def load_default_commands_from_file(self):
        """从commands.txt文件加载默认命令"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    commands = f.read().strip()
                    if commands:
                        return [cmd.strip() for cmd in commands.split('\n') if cmd.strip()]

            # 如果文件不存在，返回空列表
            return []

        except Exception as e:
            print(f"加载默认命令失败: {str(e)}")
            return []

    def create_widgets(self):
        """创建界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题区域
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 20))

        # 主标题
        title_label = ttk.Label(title_frame, text="🚀 Windows服务启动器", style='Title.TLabel')
        title_label.pack()

        # 副标题
        subtitle_label = ttk.Label(title_frame, text="在下方文本框中编辑要启动的命令，每行一个命令", style='Subtitle.TLabel')
        subtitle_label.pack(pady=(5, 0))
        
        # 命令编辑框架
        edit_frame = self.create_labelframe(main_frame, "📝 命令列表 (每行一个命令)", "15")
        edit_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        # 文本编辑框容器
        text_container = ttk.Frame(edit_frame)
        text_container.pack(fill=tk.BOTH, expand=True)

        # 文本编辑框
        self.text_area = scrolledtext.ScrolledText(
            text_container,
            wrap=tk.WORD,
            width=85,
            height=22,
            font=("Consolas", 11),
            bg='#ffffff',
            fg='#2c3e50',
            selectbackground='#3498db',
            selectforeground='white',
            insertbackground='#e74c3c',
            relief='flat',
            borderwidth=2
        )
        self.text_area.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)
        
        # 管理按钮区域
        management_frame = self.create_labelframe(main_frame, "📁 文件管理", "12")
        management_frame.pack(fill=tk.X, pady=(0, 15))

        # 管理按钮配置 - 调整按钮文本和样式
        button_config = [
            ("💾 保存", self.save_commands, "Action.TButton"),
            ("🚀 运行所有命令", self.run_commands, "Success.TButton")
        ]

        # 创建管理按钮容器 - 水平排列
        button_container = ttk.Frame(management_frame)
        button_container.pack(fill=tk.X, pady=5)

        # 创建管理按钮 - 水平排列
        for i, (text, command, style) in enumerate(button_config):
            btn = ttk.Button(
                button_container,
                text=text,
                command=command,
                style=style,
                width=20
            )
            btn.pack(side=tk.LEFT, padx=5)

            # 如果是运行按钮，保存引用
            if command == self.run_commands:
                self.run_button = btn
                print("✅ 运行按钮创建成功，使用 Success.TButton 样式")

        print("✅ 所有按钮已添加到界面")
        
        # 状态栏
        status_frame = ttk.Frame(main_frame)
        status_frame.pack(fill=tk.X, pady=(10, 0))

        # 状态栏标签
        status_title = ttk.Label(status_frame, text="📊 状态信息:", font=('Microsoft YaHei UI', 9, 'bold'), foreground='#34495e')
        status_title.pack(anchor=tk.W)

        # 状态内容
        self.status_var = tk.StringVar()
        self.status_var.set("🟢 就绪 - 编辑命令后点击保存，然后点击运行")
        status_label = ttk.Label(status_frame, textvariable=self.status_var, style='Status.TLabel', anchor=tk.W, padding="8")
        status_label.pack(fill=tk.X, pady=(2, 0))
    
    def load_commands(self):
        """加载命令"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    commands = f.read().strip()
                    if commands:
                        self.text_area.delete(1.0, tk.END)
                        self.text_area.insert(1.0, commands)
                        self.status_var.set(f"已从 {self.config_file} 加载命令")
                        return

            # 如果配置文件不存在，显示空白文本框
            self.text_area.delete(1.0, tk.END)
            self.status_var.set(f"⚠️ 配置文件 {self.config_file} 不存在，请添加命令后保存")

        except Exception as e:
            messagebox.showerror("错误", f"加载命令失败: {str(e)}")
            self.text_area.delete(1.0, tk.END)
            self.status_var.set("❌ 加载失败，请手动添加命令")
    
    def load_default_commands(self):
        """加载默认命令（从文件中读取的命令）"""
        if self.default_commands:
            default_text = '\n'.join(self.default_commands)
            self.text_area.delete(1.0, tk.END)
            self.text_area.insert(1.0, default_text)
            self.status_var.set("✅ 已加载默认命令")
        else:
            self.text_area.delete(1.0, tk.END)
            self.status_var.set("ℹ️ 没有默认命令，请手动添加")
    
    def save_commands(self):
        """保存命令（带提示）"""
        if self.save_commands_silently():
            messagebox.showinfo("成功", "命令已保存！")

    def save_commands_silently(self):
        """静默保存命令（不显示提示框）"""
        try:
            commands = self.text_area.get(1.0, tk.END).strip()
            with open(self.config_file, 'w', encoding='utf-8') as f:
                f.write(commands)
            self.status_var.set(f"✅ 命令已保存到 {self.config_file}")
            return True
        except Exception as e:
            messagebox.showerror("错误", f"保存命令失败: {str(e)}")
            self.status_var.set("❌ 保存失败")
            return False
    
    def clear_commands(self):
        """清空命令"""
        if messagebox.askyesno("确认", "确定要清空所有命令吗？"):
            self.text_area.delete(1.0, tk.END)
            self.status_var.set("🗑️ 命令已清空")
    
    def run_commands(self):
        """运行所有命令"""
        if self.is_running:
            messagebox.showwarning("警告", "正在运行命令，请稍候...")
            return
        
        # 获取当前文本框中的命令
        commands_text = self.text_area.get(1.0, tk.END).strip()
        if not commands_text:
            messagebox.showwarning("警告", "没有要运行的命令")
            return
        
        # 分割命令行
        commands = [cmd.strip() for cmd in commands_text.split('\n') if cmd.strip()]
        
        if not commands:
            messagebox.showwarning("警告", "没有有效的命令")
            return
        
        # 确认运行
        if messagebox.askyesno("确认运行", f"确定要运行 {len(commands)} 个命令吗？\n\n注意：这将启动所有列出的程序。"):
            # 在新线程中运行命令
            threading.Thread(target=self._run_commands_thread, args=(commands,), daemon=True).start()
    
    def _run_commands_thread(self, commands):
        """在后台线程中运行命令"""
        self.is_running = True
        success_count = 0
        failed_commands = []
        
        try:
            # 禁用运行按钮
            self.root.after(0, lambda: self.run_button.config(state='disabled'))
            
            for i, command in enumerate(commands):
                # 更新状态
                self.root.after(0, lambda cmd=command, i=i, total=len(commands):
                    self.status_var.set(f"🔄 正在运行命令 {i+1}/{total}: {cmd[:45]}..."))
                
                try:
                    # 移除命令两端的引号（如果有的话）
                    clean_command = command.strip().strip('"\'')
                    
                    # 检查文件是否存在
                    if not os.path.exists(clean_command):
                        failed_commands.append(f"文件不存在: {command}")
                        continue
                    
                    # 运行命令
                    subprocess.Popen([clean_command], shell=True)
                    success_count += 1
                    time.sleep(1)  # 避免同时启动太多程序
                    
                except Exception as e:
                    failed_commands.append(f"{command}: {str(e)}")
            
            # 显示结果
            result_msg = f"🎉 运行完成！\n\n✅ 成功启动: {success_count} 个程序"
            if failed_commands:
                result_msg += f"\n\n❌ 失败的命令:\n" + "\n".join(failed_commands)
            else:
                result_msg += "\n\n🎊 所有命令都成功执行！"

            self.root.after(0, lambda: messagebox.showinfo("🚀 运行结果", result_msg))
            self.root.after(0, lambda: self.status_var.set(f"✅ 运行完成 - 成功: {success_count}, 失败: {len(failed_commands)}"))
            
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"运行命令时发生错误: {str(e)}"))
            self.root.after(0, lambda: self.status_var.set("❌ 运行失败"))
        finally:
            self.is_running = False
            # 重新启用运行按钮
            self.root.after(0, lambda: self.run_button.config(state='normal'))

    def on_closing(self):
        """程序关闭时的处理"""
        # 自动保存当前命令
        self.save_commands_silently()
        self.root.destroy()

    def run(self):
        """运行主程序"""
        self.root.mainloop()


def main():
    """主函数"""
    try:
        app = SimpleLauncher()
        app.run()
    except Exception as e:
        messagebox.showerror("错误", f"程序启动失败: {str(e)}")


if __name__ == "__main__":
    main()
