('C:\\pythonwork\\service-manager\\build\\simple_launcher\\PYZ-00.pyz',
 [('__future__',
   'C:\\Development_Workspace\\python\\environment\\Lib\\__future__.py',
   'PYMODULE'),
  ('_colorize',
   'C:\\Development_Workspace\\python\\environment\\Lib\\_colorize.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Development_Workspace\\python\\environment\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Development_Workspace\\python\\environment\\Lib\\_compression.py',
   'PYMODULE'),
  ('_opcode_metadata',
   'C:\\Development_Workspace\\python\\environment\\Lib\\_opcode_metadata.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Development_Workspace\\python\\environment\\Lib\\_py_abc.py',
   'PYMODULE'),
  ('_pydatetime',
   'C:\\Development_Workspace\\python\\environment\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Development_Workspace\\python\\environment\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Development_Workspace\\python\\environment\\Lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Development_Workspace\\python\\environment\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Development_Workspace\\python\\environment\\Lib\\argparse.py',
   'PYMODULE'),
  ('ast',
   'C:\\Development_Workspace\\python\\environment\\Lib\\ast.py',
   'PYMODULE'),
  ('base64',
   'C:\\Development_Workspace\\python\\environment\\Lib\\base64.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Development_Workspace\\python\\environment\\Lib\\bisect.py',
   'PYMODULE'),
  ('bz2',
   'C:\\Development_Workspace\\python\\environment\\Lib\\bz2.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Development_Workspace\\python\\environment\\Lib\\calendar.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Development_Workspace\\python\\environment\\Lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Development_Workspace\\python\\environment\\Lib\\contextvars.py',
   'PYMODULE'),
  ('copy',
   'C:\\Development_Workspace\\python\\environment\\Lib\\copy.py',
   'PYMODULE'),
  ('csv',
   'C:\\Development_Workspace\\python\\environment\\Lib\\csv.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Development_Workspace\\python\\environment\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Development_Workspace\\python\\environment\\Lib\\datetime.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Development_Workspace\\python\\environment\\Lib\\decimal.py',
   'PYMODULE'),
  ('dis',
   'C:\\Development_Workspace\\python\\environment\\Lib\\dis.py',
   'PYMODULE'),
  ('email',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Development_Workspace\\python\\environment\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\Development_Workspace\\python\\environment\\Lib\\fnmatch.py',
   'PYMODULE'),
  ('fractions',
   'C:\\Development_Workspace\\python\\environment\\Lib\\fractions.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Development_Workspace\\python\\environment\\Lib\\getopt.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Development_Workspace\\python\\environment\\Lib\\gettext.py',
   'PYMODULE'),
  ('glob',
   'C:\\Development_Workspace\\python\\environment\\Lib\\glob.py',
   'PYMODULE'),
  ('gzip',
   'C:\\Development_Workspace\\python\\environment\\Lib\\gzip.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Development_Workspace\\python\\environment\\Lib\\hashlib.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Development_Workspace\\python\\environment\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Development_Workspace\\python\\environment\\Lib\\inspect.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Development_Workspace\\python\\environment\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('json',
   'C:\\Development_Workspace\\python\\environment\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Development_Workspace\\python\\environment\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Development_Workspace\\python\\environment\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Development_Workspace\\python\\environment\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'C:\\Development_Workspace\\python\\environment\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma',
   'C:\\Development_Workspace\\python\\environment\\Lib\\lzma.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Development_Workspace\\python\\environment\\Lib\\numbers.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Development_Workspace\\python\\environment\\Lib\\opcode.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Development_Workspace\\python\\environment\\Lib\\pathlib\\__init__.py',
   'PYMODULE'),
  ('pathlib._abc',
   'C:\\Development_Workspace\\python\\environment\\Lib\\pathlib\\_abc.py',
   'PYMODULE'),
  ('pathlib._local',
   'C:\\Development_Workspace\\python\\environment\\Lib\\pathlib\\_local.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Development_Workspace\\python\\environment\\Lib\\pickle.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Development_Workspace\\python\\environment\\Lib\\pprint.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Development_Workspace\\python\\environment\\Lib\\py_compile.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Development_Workspace\\python\\environment\\Lib\\quopri.py',
   'PYMODULE'),
  ('random',
   'C:\\Development_Workspace\\python\\environment\\Lib\\random.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Development_Workspace\\python\\environment\\Lib\\selectors.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Development_Workspace\\python\\environment\\Lib\\shutil.py',
   'PYMODULE'),
  ('signal',
   'C:\\Development_Workspace\\python\\environment\\Lib\\signal.py',
   'PYMODULE'),
  ('socket',
   'C:\\Development_Workspace\\python\\environment\\Lib\\socket.py',
   'PYMODULE'),
  ('statistics',
   'C:\\Development_Workspace\\python\\environment\\Lib\\statistics.py',
   'PYMODULE'),
  ('string',
   'C:\\Development_Workspace\\python\\environment\\Lib\\string.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Development_Workspace\\python\\environment\\Lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Development_Workspace\\python\\environment\\Lib\\subprocess.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Development_Workspace\\python\\environment\\Lib\\tarfile.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Development_Workspace\\python\\environment\\Lib\\tempfile.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Development_Workspace\\python\\environment\\Lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'C:\\Development_Workspace\\python\\environment\\Lib\\threading.py',
   'PYMODULE'),
  ('tkinter',
   'C:\\Development_Workspace\\python\\environment\\Lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'C:\\Development_Workspace\\python\\environment\\Lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'C:\\Development_Workspace\\python\\environment\\Lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.font',
   'C:\\Development_Workspace\\python\\environment\\Lib\\tkinter\\font.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'C:\\Development_Workspace\\python\\environment\\Lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.scrolledtext',
   'C:\\Development_Workspace\\python\\environment\\Lib\\tkinter\\scrolledtext.py',
   'PYMODULE'),
  ('tkinter.ttk',
   'C:\\Development_Workspace\\python\\environment\\Lib\\tkinter\\ttk.py',
   'PYMODULE'),
  ('token',
   'C:\\Development_Workspace\\python\\environment\\Lib\\token.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\Development_Workspace\\python\\environment\\Lib\\tokenize.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Development_Workspace\\python\\environment\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('typing',
   'C:\\Development_Workspace\\python\\environment\\Lib\\typing.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Development_Workspace\\python\\environment\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Development_Workspace\\python\\environment\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Development_Workspace\\python\\environment\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'C:\\Development_Workspace\\python\\environment\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\Development_Workspace\\python\\environment\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE')])
